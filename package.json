{"name": "univer-sheet-start-kit", "type": "module", "version": "0.9.1", "private": true, "packageManager": "pnpm@10.12.3+sha512.467df2c586056165580ad6dfb54ceaad94c5a30f80893ebdec5a44c5aa73c205ae4a5bb9d5ed6bb84ea7c249ece786642bbb49d06a307df218d03da41c317417", "description": "A starter kit for Univer Sheet", "scripts": {"dev": "vite --host", "build": "vite build"}, "dependencies": {"@univerjs/core": "^0.10.0", "@univerjs/data-validation": "^0.10.0", "@univerjs/design": "^0.10.0", "@univerjs/docs": "^0.10.0", "@univerjs/docs-ui": "^0.10.0", "@univerjs/engine-formula": "^0.10.0", "@univerjs/engine-render": "^0.10.0", "@univerjs/find-replace": "^0.10.1", "@univerjs/rpc": "^0.10.0", "@univerjs/sheets": "^0.10.0", "@univerjs/sheets-data-validation": "^0.10.0", "@univerjs/sheets-data-validation-ui": "^0.10.0", "@univerjs/sheets-filter": "^0.10.0", "@univerjs/sheets-filter-ui": "^0.10.0", "@univerjs/sheets-find-replace": "^0.10.1", "@univerjs/sheets-formula": "^0.10.0", "@univerjs/sheets-formula-ui": "^0.10.0", "@univerjs/sheets-note": "^0.10.0", "@univerjs/sheets-note-ui": "^0.10.0", "@univerjs/sheets-numfmt": "^0.10.0", "@univerjs/sheets-numfmt-ui": "^0.10.0", "@univerjs/sheets-ui": "^0.10.0", "@univerjs/telemetry": "^0.10.0", "@univerjs/ui": "^0.10.0", "install": "^0.13.0", "npm": "^11.5.1", "react": "^18.3.1", "react-dom": "^18.3.1", "rxjs": "^7.8.2"}, "devDependencies": {"@univerjs/vite-plugin": "^0.5.1", "typescript": "^5.8.3", "vite": "^6.3.5"}}