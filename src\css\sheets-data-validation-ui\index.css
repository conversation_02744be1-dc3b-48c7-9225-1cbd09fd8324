body .univer-absolute {
  position: absolute;
}
body .univer-relative {
  position: relative;
}
body .-univer-bottom-0\.5 {
  bottom: -2px;
}
body .-univer-right-0\.5 {
  right: -2px;
}
body .univer-mx-1 {
  margin-left: 4px;
  margin-right: 4px;
}
body .univer-mb-1 {
  margin-bottom: 4px;
}
body .univer-mb-2 {
  margin-bottom: 8px;
}
body .univer-ml-1 {
  margin-left: 4px;
}
body .univer-mt-2 {
  margin-top: 8px;
}
body .univer-mt-4 {
  margin-top: 16px;
}
body .univer-box-border {
  box-sizing: border-box;
}
body .univer-inline-block {
  display: inline-block;
}
body .univer-flex {
  display: flex;
}
body .univer-inline-flex {
  display: inline-flex;
}
body .univer-grid {
  display: grid;
}
body .univer-hidden {
  display: none;
}
body .univer-h-3 {
  height: 12px;
}
body .univer-h-4 {
  height: 16px;
}
body .univer-h-5 {
  height: 20px;
}
body .univer-h-6 {
  height: 24px;
}
body .univer-h-7 {
  height: 28px;
}
body .univer-h-8 {
  height: 32px;
}
body .univer-h-\[300px\] {
  height: 300px;
}
body .univer-h-full {
  height: 100%;
}
body .univer-max-h-\[500px\] {
  max-height: 500px;
}
body .univer-min-h-\[300px\] {
  min-height: 300px;
}
body .univer-w-3 {
  width: 12px;
}
body .univer-w-6 {
  width: 24px;
}
body .univer-w-\[400px\] {
  width: 400px;
}
body .univer-w-full {
  width: 100%;
}
body .univer-max-w-\[245px\] {
  max-width: 245px;
}
body .univer-flex-1 {
  flex: 1 1 0%;
}
body .univer-flex-shrink {
  flex-shrink: 1;
}
body .univer-flex-shrink-0 {
  flex-shrink: 0;
}
body .univer-flex-grow {
  flex-grow: 1;
}
body .univer-flex-grow-0 {
  flex-grow: 0;
}
body .univer-cursor-pointer {
  cursor: pointer;
}
body .univer-grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}
body .univer-flex-col {
  flex-direction: column;
}
body .univer-flex-nowrap {
  flex-wrap: nowrap;
}
body .univer-items-center {
  align-items: center;
}
body .univer-justify-start {
  justify-content: flex-start;
}
body .univer-justify-center {
  justify-content: center;
}
body .univer-justify-between {
  justify-content: space-between;
}
body .univer-gap-2 {
  gap: 8px;
}
body .univer-gap-4 {
  gap: 16px;
}
body .univer-overflow-auto {
  overflow: auto;
}
body .univer-overflow-hidden {
  overflow: hidden;
}
body .univer-text-ellipsis {
  text-overflow: ellipsis;
}
body .univer-whitespace-nowrap {
  white-space: nowrap;
}
body .univer-rounded-full {
  border-radius: 9999px;
}
body .univer-rounded-lg {
  border-radius: 8px;
}
body .univer-rounded-md {
  border-radius: 6px;
}
body .univer-border {
  border-width: 1px;
}
body .univer-border-solid {
  border-style: solid;
}
body .univer-border-\[rgba\(13\,13\,13\,0\.06\)\] {
  border-color: #0d0d0d0f;
}
body .univer-border-transparent {
  border-color: transparent;
}
body .univer-bg-gray-300 {
  background-color: var(--univer-gray-300);
}
body .univer-bg-white {
  background-color: var(--univer-white);
}
body .univer-p-0\.5 {
  padding: 2px;
}
body .univer-p-2 {
  padding: 8px;
}
body .univer-p-4 {
  padding: 16px;
}
body .univer-px-1\.5 {
  padding-left: 6px;
  padding-right: 6px;
}
body .univer-px-2 {
  padding-left: 8px;
  padding-right: 8px;
}
body .univer-py-0\.5 {
  padding-top: 2px;
  padding-bottom: 2px;
}
body .univer-py-2\.5 {
  padding-top: 10px;
  padding-bottom: 10px;
}
body .univer-pb-0 {
  padding-bottom: 0;
}
body .univer-pl-5 {
  padding-left: 20px;
}
body .univer-pr-0\.5 {
  padding-right: 2px;
}
body .univer-pr-2 {
  padding-right: 8px;
}
body .univer-pt-0 {
  padding-top: 0;
}
body .univer-pt-2 {
  padding-top: 8px;
}
body .univer-text-sm {
  font-size: 14px;
  line-height: 20px;
}
body .univer-text-xs {
  font-size: 12px;
  line-height: 16px;
}
body .univer-font-bold {
  font-weight: 700;
}
body .univer-text-\[\#418F1F\] {
  --univer-tw-text-opacity: 1;
  color: #418f1f;
  color: rgba(65, 143, 31, var(--univer-tw-text-opacity, 1));
}
body .univer-text-gray-400 {
  color: var(--univer-gray-400);
}
body .univer-text-gray-500 {
  color: var(--univer-gray-500);
}
body .univer-text-gray-900 {
  color: var(--univer-gray-900);
}
body .univer-text-primary-500 {
  color: var(--univer-primary-500);
}
body .univer-shadow-lg {
  --univer-tw-shadow: 0px 4px 6px 0px rgba(30, 40, 77, 0.05),
    0px 10px 15px -3px rgba(30, 40, 77, 0.1);
  --univer-tw-shadow-colored: 0px 4px 6px 0px var(--univer-tw-shadow-color),
    0px 10px 15px -3px var(--univer-tw-shadow-color);
  box-shadow: 0 0 #0000, 0 0 #0000, 0 4px 6px #1e284d0d,
    0 10px 15px -3px #1e284d1a;
  box-shadow: var(--univer-tw-ring-offset-shadow, 0 0 rgba(0, 0, 0, 0)),
    var(--univer-tw-ring-shadow, 0 0 rgba(0, 0, 0, 0)), var(--univer-tw-shadow);
}
body .univer-transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 0.15s;
}
.hover\:univer-bg-gray-50:hover {
  background-color: var(--univer-gray-50);
}
.hover\:univer-ring-2:hover {
  --univer-tw-ring-offset-shadow: var(--univer-tw-ring-inset) 0 0 0
    var(--univer-tw-ring-offset-width) var(--univer-tw-ring-offset-color);
  --univer-tw-ring-shadow: var(--univer-tw-ring-inset) 0 0 0
    calc(2px + var(--univer-tw-ring-offset-width)) var(--univer-tw-ring-color);
  box-shadow: var(--univer-tw-ring-offset-shadow), var(--univer-tw-ring-shadow),
    0 0 #0000;
  box-shadow: var(--univer-tw-ring-offset-shadow), var(--univer-tw-ring-shadow),
    var(--univer-tw-shadow, 0 0 rgba(0, 0, 0, 0));
}
.hover\:univer-ring-offset-2:hover {
  --univer-tw-ring-offset-width: 2px;
}
.hover\:univer-ring-offset-white:hover {
  --univer-tw-ring-offset-color: var(--univer-white);
}
.dark\:\!univer-border-\[rgba\(255\,255\,255\,0\.06\)\]:where(
    .univer-dark,
    .univer-dark * body
  ) {
  border-color: #ffffff0f !important;
}
body .dark\:\!univer-border-gray-600:where(.univer-dark, .univer-dark *) {
  border-color: var(--univer-gray-600) !important;
}
body .dark\:\!univer-bg-gray-700:where(.univer-dark, .univer-dark *) {
  background-color: var(--univer-gray-700) !important;
}
body .dark\:\!univer-text-gray-200:where(.univer-dark, .univer-dark *) {
  color: var(--univer-gray-200) !important;
}
body .dark\:\!univer-text-gray-500:where(.univer-dark, .univer-dark *) {
  color: var(--univer-gray-500) !important;
}
body .dark\:\!univer-text-white:where(.univer-dark, .univer-dark *) {
  color: var(--univer-white) !important;
}
body
  .dark\:hover\:\!univer-bg-gray-900:hover:where(.univer-dark, .univer-dark *) {
  background-color: var(--univer-gray-900) !important;
}
body .\[\&\:hover_a\]\:univer-inline-block:hover a {
  display: inline-block;
}
