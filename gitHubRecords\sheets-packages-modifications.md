# sheets-data-validation-ui, sheets-filter-ui, sheets-ui 修改记录

## 概述
本文档整理了 sheets-data-validation-ui、sheets-filter-ui、sheets-ui 包的所有 Git 提交修改记录，使用 git diff 格式展示具体的代码变更。

---

## sheets-data-validation-ui 修改记录

### 1. 修改下拉 (26e2760)
**提交时间**: 2025-07-30 11:24:51
**作者**: yangan

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-multiple-widget.ts`
```diff
@@ -120,33 +120,19 @@ export class DropdownMultipleWidget implements IBaseDataValidationWidget {
         ctx.rect(0, 0, cellWidth - ICON_PLACE, cellHeight);
         ctx.clip();
         ctx.translateWithPrecision(CELL_PADDING_H, CELL_PADDING_V);
-        let top = 0;
-        switch (vt) {
-            case VerticalAlign.MIDDLE:
-                top = (layout.contentHeight - layout.totalHeight) / 2;
-                break;
-            case VerticalAlign.BOTTOM:
-                top = (layout.contentHeight - layout.totalHeight);
-                break;
-            default:
-                break;
-        }
+
+        // 修改：选项整体垂直居中
+        const top = (layout.contentHeight - layout.totalHeight) / 2;
         ctx.translateWithPrecision(0, top);
+
         layout.lines.forEach((line, index) => {
             ctx.save();
-            const { width, height, items } = line;
-            let left = 0;
-            switch (ht) {
-                case HorizontalAlign.RIGHT:
-                    left = layout.contentWidth - width;
-                    break;
-                case HorizontalAlign.CENTER:
-                    left = (layout.contentWidth - width) / 2;
-                    break;
-                default:
-                    break;
-            }
+            const { height, items } = line;
+
+            // 修改：选项整体靠左对齐
+            const left = 0;
             ctx.translate(left, index * (height + MARGIN_V));
+
             items.forEach((item) => {
                 ctx.save();
                 ctx.translateWithPrecision(item.left, 0);
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-widget.ts`
```diff
@@ -28,17 +28,17 @@ import { DROP_DOWN_DEFAULT_COLOR } from '../../const';
 /**
  * padding in Capsule
  */
-const PADDING_H = 4;
+const PADDING_H = 6;
 const ICON_SIZE = 4;
 const ICON_PLACE = 14;
-const PADDING_V = 1;
+const PADDING_V = 2;

 /**
  * margin for Capsule, that means distance between capsule and cell border
  */
 const MARGIN_H = 6;
 const MARGIN_V = 3;
-const RADIUS_BG = 8;
+const RADIUS_BG = 4;
 const DROP_DOWN_ICON_COLOR = '#565656';
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/dropdown.ts`
```diff
@@ -18,9 +18,8 @@ import type { IShapeProps, UniverRenderingContext } from '@univerjs/engine-rende
 import type { IDropdownLayoutInfo } from './layout';
 import { Rect, Shape } from '@univerjs/engine-render';
 import { DROP_DOWN_DEFAULT_COLOR } from '../../../const';
-import { PADDING_H } from './layout';

-const RADIUS = 8;
+const RADIUS = 4;

 export interface IDropdownProps extends IShapeProps {
     fontString: string;
@@ -41,10 +40,18 @@ export class Dropdown extends Shape<IDropdownProps> {
             radius: RADIUS,
             fill: fill || DROP_DOWN_DEFAULT_COLOR,
         });
-        ctx.translateWithPrecision(PADDING_H, layout.ba);
+
+        // 修改：文字在背景框内水平和垂直居中
         ctx.font = fontString;
         ctx.fillStyle = color;
-        ctx.fillText(text, 0, 0);
+        ctx.textAlign = 'center';
+        ctx.textBaseline = 'middle';
+
+        // 在背景框的中心位置绘制文字
+        const centerX = layout.width / 2;
+        const centerY = layout.height / 2;
+        ctx.fillText(text, centerX, centerY);
+
         ctx.restore();
     }
 }
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/layout.ts`
```diff
@@ -18,8 +18,8 @@ import type { IStyleData, Nullable } from '@univerjs/core';
 import type { IDocumentSkeletonFontStyle } from '@univerjs/engine-render';
 import { FontCache, getFontStyleString } from '@univerjs/engine-render';

-export const PADDING_H = 4;
-export const PADDING_V = 0;
+export const PADDING_H = 6;
+export const PADDING_V = 2;
 export const MARGIN_H = 4;
 export const MARGIN_V = 4;
 export const CELL_PADDING_H = 6;
@@ -69,42 +69,41 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
         text: item,
     }));

-    let currentLine: IDropdownLine | undefined;
-    const lines: IDropdownLine[] = [];
+    // 修改：所有选项都放在一行，不换行
+    const singleLine: IDropdownLine = {
+        width: 0,
+        height: 0,
+        items: [],
+    };

-    textLayout.forEach((item) => {
+    let currentLeft = 0;
+    textLayout.forEach((item, index) => {
         const { layout } = item;
         const { width, height } = layout;

-        if (!currentLine || ((currentLine.width + width + MARGIN_H) > widthAvailableForContent)) {
-            currentLine = {
-                width,
-                height,
-                items: [{
-                    ...item,
-                    left: 0,
-                }],
-            };
-            lines.push(currentLine);
-        } else {
-            currentLine.items.push({
+        // 只有在宽度允许的情况下才添加选项，否则截断
+        if (currentLeft + width <= widthAvailableForContent) {
+            singleLine.items.push({
                 ...item,
-                left: currentLine.width + MARGIN_H,
+                left: currentLeft,
             });
-            currentLine.width = currentLine.width + width + MARGIN_H;
-        }
-    });
-    let totalHeight = 0;
-    let maxLineWidth = 0;
-    lines.forEach((line, index) => {
-        maxLineWidth = Math.max(maxLineWidth, line.width);
-        if (index === lines.length - 1) {
-            totalHeight += line.height;
-        } else {
-            totalHeight += line.height + MARGIN_V;
+            singleLine.width = currentLeft + width;
+            singleLine.height = Math.max(singleLine.height, height);
+
+            // 如果不是最后一个选项，添加间距
+            if (index < textLayout.length - 1) {
+                currentLeft += width + MARGIN_H;
+            } else {
+                currentLeft += width;
+            }
         }
+        // 超出宽度的选项直接忽略（截断）
     });

+    const lines = [singleLine];
+    const totalHeight = singleLine.height;
+    const maxLineWidth = singleLine.width;
+
     return {
         lines,
         totalHeight,
```

---

### 2. 修改下拉样式 (02f87c4)
**提交时间**: 2025-07-30 11:29:18
**作者**: yangan

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-multiple-widget.ts`
```diff
@@ -136,6 +136,14 @@ export class DropdownMultipleWidget implements IBaseDataValidationWidget {
             items.forEach((item) => {
                 ctx.save();
                 ctx.translateWithPrecision(item.left, 0);
+
+                // 如果选项被截断，设置裁剪区域
+                if (item.clipped && item.clippedWidth) {
+                    ctx.beginPath();
+                    ctx.rect(0, 0, item.clippedWidth, item.layout.height);
+                    ctx.clip();
+                }
+
                 Dropdown.drawWith(ctx as UniverRenderingContext, {
                     ...fontStyle,
                     info: item,
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/layout.ts`
```diff
@@ -57,7 +57,11 @@ export interface IDropdownLayoutInfo {
 export interface IDropdownLine {
     width: number;
     height: number;
-    items: (IDropdownLayoutInfo & { left: number })[];
+    items: (IDropdownLayoutInfo & {
+        left: number;
+        clipped?: boolean;
+        clippedWidth?: number;
+    })[];
 }

 export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFontStyle, cellWidth: number, cellHeight: number) {
@@ -81,8 +85,9 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
         const { layout } = item;
         const { width, height } = layout;

-        // 只有在宽度允许的情况下才添加选项，否则截断
+        // 检查是否有足够空间显示完整选项
         if (currentLeft + width <= widthAvailableForContent) {
+            // 完整显示选项
             singleLine.items.push({
                 ...item,
                 left: currentLeft,
@@ -96,8 +101,24 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
             } else {
                 currentLeft += width;
             }
+        } else if (currentLeft < widthAvailableForContent) {
+            // 部分显示选项（能显示多少就显示多少）
+            const remainingWidth = widthAvailableForContent - currentLeft;
+            if (remainingWidth > 0) {
+                singleLine.items.push({
+                    ...item,
+                    left: currentLeft,
+                    // 添加一个标记表示这个选项被截断了
+                    clipped: true,
+                    clippedWidth: remainingWidth,
+                });
+                singleLine.width = widthAvailableForContent;
+                singleLine.height = Math.max(singleLine.height, height);
+            }
+            // 后续选项都无法显示，直接跳出循环
+            return;
         }
-        // 超出宽度的选项直接忽略（截断）
+        // 完全超出宽度的选项直接忽略
     });

     const lines = [singleLine];
```

---

## sheets-filter-ui 修改记录

### 1. 修改图标 (1dd156a)
**提交时间**: 2025-07-30 16:20:56
**作者**: yangan

#### 文件: `packages/sheets-filter-ui/src/views/render-modules/sheets-filter.render-controller.ts`
```diff
@@ -157,7 +157,8 @@ export class SheetsFilterRenderController extends RxDisposable implements IRende
             // In other cases we need to draw the button, and we need to take care of the position and clipping.
             const hasCriteria = !!filterModel.getFilterColumn(col);
             const iconStartX = endX - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
-            const iconStartY = endY - FILTER_ICON_SIZE - FILTER_ICON_PADDING;
+            // Center the icon vertically in the cell
+            const iconStartY = startY + (cellHeight - FILTER_ICON_SIZE) / 2;
             const props: ISheetsFilterButtonShapeProps = {
                 left: iconStartX,
                 top: iconStartY,
```

#### 文件: `packages/sheets-filter-ui/src/views/widgets/drawings.ts`
```diff
@@ -19,9 +19,8 @@ import { Rect } from '@univerjs/engine-render';

 const BUTTON_VIEWPORT = 16;

-// This path is deprecated. We need to get rounded edge of the stroked line.
-// export const FILTER_BUTTON_HAS_CRITERIA = new Path2D('M3 4H13 M4.5 8H11.5 M6 12H10');
-export const FILTER_BUTTON_EMPTY = new Path2D('M3.30363 3C2.79117 3 2.51457 3.60097 2.84788 3.99024L6.8 8.60593V12.5662C6.8 12.7184 6.8864 12.8575 7.02289 12.9249L8.76717 13.7863C8.96655 13.8847 9.2 13.7396 9.2 13.5173V8.60593L13.1521 3.99024C13.4854 3.60097 13.2088 3 12.6964 3H3.30363Z');
+// Triangle dropdown icon path - pointing down
+export const FILTER_BUTTON_TRIANGLE = new Path2D('M4 6L8 10L12 6Z');

 export class FilterButton {
     static drawNoCriteria(ctx: UniverRenderingContext2D, size: number, fgColor: string, bgColor: string): void {
@@ -34,19 +33,9 @@ export class FilterButton {
             fill: bgColor,
         });

-        ctx.lineCap = 'square';
-        ctx.strokeStyle = fgColor;
         ctx.scale(size / BUTTON_VIEWPORT, size / BUTTON_VIEWPORT);
-        ctx.beginPath();
-        ctx.lineWidth = 1;
-        ctx.lineCap = 'round';
-        ctx.moveTo(3, 4);
-        ctx.lineTo(13, 4);
-        ctx.moveTo(4.5, 8);
-        ctx.lineTo(11.5, 8);
-        ctx.moveTo(6, 12);
-        ctx.lineTo(10, 12);
-        ctx.stroke();
+        ctx.fillStyle = fgColor;
+        ctx.fill(FILTER_BUTTON_TRIANGLE);
         ctx.restore();
     }

@@ -62,7 +51,7 @@ export class FilterButton {

         ctx.scale(size / BUTTON_VIEWPORT, size / BUTTON_VIEWPORT);
         ctx.fillStyle = fgColor;
-        ctx.fill(FILTER_BUTTON_EMPTY);
+        ctx.fill(FILTER_BUTTON_TRIANGLE);
         ctx.restore();
     }
 }
```

---

### 2. 优化筛选 (7aee833)
**提交时间**: 2025-07-30 17:16:41
**作者**: yangan

#### 文件: `packages/sheets-filter-ui/src/views/widgets/filter-button.shape.ts`
```diff
@@ -92,7 +92,12 @@ export class SheetsFilterButtonShape extends Shape<ISheetsFilterButtonShapeProps

         const { hasCriteria } = this._filterParams!;

-        const fgColor = this._themeService.getColorFromTheme('primary.600');
+        // 根据是否有筛选条件决定图标颜色
+        // 默认状态（无筛选条件）：黑色
+        // 选中状态（有筛选条件）：蓝色
+        const fgColor = hasCriteria
+            ? this._themeService.getColorFromTheme('primary.600')  // 蓝色
+            : this._themeService.getColorFromTheme('black');       // 黑色
         const bgColor = this._hovered
             ? this._themeService.getColorFromTheme('gray.50')
             : 'rgba(255, 255, 255, 1.0)';
```

---

### 3. 修改下拉框 (fbf9080)
**提交时间**: 2025-07-30 18:30:13
**作者**: yangan

#### 文件: `packages/sheets-filter-ui/src/views/render-modules/sheets-filter.render-controller.ts`
```diff
@@ -200,7 +200,7 @@ export class SheetsFilterRenderController extends RxDisposable implements IRende

                 cell.fontRenderExtension = {
                     ...cell?.fontRenderExtension,
-                    rightOffset: FILTER_ICON_SIZE,
+                    rightOffset: FILTER_ICON_SIZE + FILTER_ICON_PADDING,  // 图标大小 + 右边距
                 };

                 return next(cell);
```

#### 文件: `packages/sheets-filter-ui/src/views/widgets/filter-button.shape.ts`
```diff
@@ -22,7 +22,7 @@ import { FILTER_PANEL_OPENED_KEY, OpenFilterPanelOperation } from '../../command
 import { FilterButton } from './drawings';

 export const FILTER_ICON_SIZE = 16;
-export const FILTER_ICON_PADDING = 1;
+export const FILTER_ICON_PADDING = 10;  // 右边留出10像素位置

 export interface ISheetsFilterButtonShapeProps extends IShapeProps {
     cellWidth: number;
@@ -100,7 +100,7 @@ export class SheetsFilterButtonShape extends Shape<ISheetsFilterButtonShapeProps
             : this._themeService.getColorFromTheme('black');       // 黑色
         const bgColor = this._hovered
             ? this._themeService.getColorFromTheme('gray.50')
-            : 'rgba(255, 255, 255, 1.0)';
+            : 'rgba(255, 255, 255, 0.0)';  // 透明背景

         if (hasCriteria) {
             FilterButton.drawHasCriteria(ctx, FILTER_ICON_SIZE, fgColor, bgColor);
```

---

## sheets-ui 修改记录

### 1. 修改线的宽度 (c2cf59d)
**提交时间**: 2025-07-31 03:17:02
**作者**: yangan

#### 文件: `packages/sheets-ui/src/controllers/render-controllers/freeze.render-controller.ts`
```diff
@@ -100,7 +100,7 @@ export const FREEZE_COLUMN_MAIN_NAME = '__SpreadsheetFreezeColumnMainName__';

 export const FREEZE_COLUMN_HEADER_NAME = '__SpreadsheetFreezeColumnHeaderName__';

-const FREEZE_SIZE_NORMAL = 2;
+const FREEZE_SIZE_NORMAL = 1;

 const AUXILIARY_CLICK_HIDDEN_OBJECT_TRANSPARENCY = 0.01;
```

---

### 2. 换行问题备份 (154ea8d)
**提交时间**: 2025-07-31 11:18:05
**作者**: yangan

#### 文件: `packages/sheets-ui/src/services/editor/cell-editor-resize.service.ts`
```diff
@@ -203,9 +203,9 @@ export class SheetCellEditorResizeService extends Disposable {
             editorWidth = size.actualWidth * scaleX + EDITOR_INPUT_SELF_EXTEND_GAP * scaleX;
         }

-        // Scaling is handled by the renderer, so the skeleton only accepts the original width and height, which need to be divided by the magnification factor.
-        documentDataModel?.updateDocumentDataPageSize(editorWidth / scaleX);

+        // documentDataModel?.updateDocumentDataPageSize(Number.POSITIVE_INFINITY);
+        documentDataModel?.updateDocumentDataPageSize(editorWidth / scaleX);
         /**
          * Do not rely on cell layout logic, depend on the document's internal alignment logic.
          */
```

---

## package.json 构建脚本修改记录

### 1. 修改下拉 (26e2760)
```diff
@@ -36,6 +36,7 @@
         "build": "turbo build --concurrency=30% --filter=!./common/*",
         "build:ci": "turbo build --concurrency=100% --filter=!./common/*",
         "build:filter": "pnpm --filter @univerjs/sheets-filter --filter @univerjs/sheets-filter-ui --filter @univerjs/sheets-table-ui build",
+        "build:data-validation": "pnpm --filter @univerjs/sheets-data-validation-ui build",
         "build:demo": "pnpm --filter univer-examples build:demo",
         "build:e2e": "pnpm --filter univer-examples build:e2e",
         "serve:e2e": "serve ./examples/local",
```

### 2. 修改指令 (b87e99d)
```diff
@@ -37,6 +37,9 @@
         "build:ci": "turbo build --concurrency=100% --filter=!./common/*",
         "build:filter": "pnpm --filter @univerjs/sheets-filter --filter @univerjs/sheets-filter-ui --filter @univerjs/sheets-table-ui build",
         "build:data-validation": "pnpm --filter @univerjs/sheets-data-validation-ui build",
+
+        "build:sheets-ui": "pnpm --filter @univerjs/sheets-ui build",
+
         "build:demo": "pnpm --filter univer-examples build:demo",
         "build:e2e": "pnpm --filter univer-examples build:e2e",
         "serve:e2e": "serve ./examples/local",
```

### 3. 换行问题备份 (154ea8d)
```diff
@@ -37,8 +37,9 @@
         "build:ci": "turbo build --concurrency=100% --filter=!./common/*",
         "build:filter": "pnpm --filter @univerjs/sheets-filter --filter @univerjs/sheets-filter-ui --filter @univerjs/sheets-table-ui build",
         "build:data-validation": "pnpm --filter @univerjs/sheets-data-validation-ui build",
-
-        "build:sheets-ui": "pnpm --filter @univerjs/sheets-ui build",
+        "build:core": "pnpm --filter @univerjs/core build",
+        "build:no-wrap": "pnpm --filter @univerjs/core --filter @univerjs/engine-render --filter @univerjs/sheets-ui --filter @univerjs/docs-ui --filter @univerjs/slides-ui build",
+        "build:sheets-ui": "pnpm --filter @univerjs/sheets-ui --filter @univerjs/sheets build --filter @univerjs/sheets-formula-ui build" ,

         "build:demo": "pnpm --filter univer-examples build:demo",
         "build:e2e": "pnpm --filter univer-examples build:e2e",
```

---

## 修改总结

### 按包分类的主要变更:

**sheets-data-validation-ui**:
- 下拉选项布局优化：垂直居中、左对齐、单行显示
- 样式调整：内边距、圆角、文字居中
- 截断处理：超长选项的部分显示和裁剪

**sheets-filter-ui**:
- 图标更新：三角形下拉图标替换复杂路径
- 颜色优化：根据筛选状态动态显示颜色
- 布局调整：图标垂直居中、增加间距、透明背景

**sheets-ui**:
- 冻结线宽度：从2像素减少到1像素
- 编辑器换行：修复文本换行问题的尝试

**构建脚本**:
- 新增多个独立构建命令
- 支持按模块单独构建
