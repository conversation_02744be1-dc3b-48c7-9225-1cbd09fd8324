# 表格列宽度设置说明

## 宽度类型定义
- **short**: 120px - 适用于简短内容
- **medium**: 200px - 适用于中等长度内容  
- **long**: 300px - 适用于较长内容

## 各字段宽度设置及原因

### 1. 职位名称 (name) - `long`
**原因**: 职位名称通常较长，如"通信建设岗1"、"新闻编辑岗"等，需要更多空间完整显示

### 2. 职位性质 (job_nature) - `medium`
**原因**: 职位性质如"专技岗位（十二级）"、"管理岗位（九级）"，中等长度

### 3. 招录人数（数字）(need_num) - `short`
**原因**: 数字字段，内容简短，如"1"、"10"

### 4. 报考地区 (region_address) - `medium`
**原因**: 地区信息如"重庆-重庆市-渝中"，中等长度

### 5. 上级单位 (department) - `medium`
**原因**: 单位名称如"市委办公厅"、"市委宣传部"，中等长度

### 6. 工作单位 (work_unit) - `long`
**原因**: 工作单位名称可能较长，如"市党政专用通信服务中心"、"重庆日报报业集团"

### 7. 联系电话 (contact_phone) - `long`
**原因**: 联系电话包含地址和电话号码，内容较长，如"渝中区中山四路36号；杨老师，023-63899155"

### 8. 学历要求（原文）(education_range_text) - `medium`
**原因**: 学历要求原文如"本科以上学历及相应学位"，中等长度

### 9. 学历要求（数据）(education_range) - `long`
**原因**: 多选数组类型，需要显示多个学历标签，如"本科,硕士研究生,博士研究生"

### 10. 专业要求（原文）(major_text) - `long`
**原因**: 专业要求原文很长，包含详细的专业列表和代码

### 11. 本科专业要求（数据）(major_6) - `long`
**原因**: 专业数据可能包含多个专业代码，内容较长

### 12. 研究生专业要求（数据）(major_8) - `long`
**原因**: 研究生专业数据，内容较长

### 13. 年龄要求（原文）(age_text) - `short`
**原因**: 年龄要求如"35周岁以下"、"45周岁以下"，内容简短

### 14. 最大年龄要求 (max_age) - `short`
**原因**: 数字字段，内容简短，如"35"、"45"

### 15. 性别要求（数据）(gender) - `short`
**原因**: 性别选择，内容简短，如"男"、"女"

### 16. 政治面貌（原文）(politics_face_text) - `medium`
**原因**: 政治面貌原文，中等长度，如"中共党员,中共预备党员"

### 17. 政治面貌（数据）(politics_face) - `long`
**原因**: 多选政治面貌，可能显示多个标签

### 18. 基层工作经验（数据）(work_years) - `medium`
**原因**: 工作经验选择，中等长度，如"二年"、"无工作经验"

### 19. 最低服务年限 (service_years) - `medium`
**原因**: 服务年限，中等长度，如"不约定服务期"

### 20. 其他要求 (other_require) - `long`
**原因**: 其他要求内容较长，包含详细条件，如"须同时满足以下条件：1.中共党员（含预备党员）；2.具有2年以上岗位相关工作经历；3.限男性"

### 21. 试卷类型 (paper_type) - `medium`
**原因**: 试卷类型，中等长度，如"自然科学专技类（C类）"、"综合管理类（A类）"

### 22. 备注 (remarks) - `long`
**原因**: 备注信息可能较长，包含详细说明

## 设置原则

### Short (120px) 适用场景：
- 数字字段（招录人数、年龄等）
- 简短的分类信息（性别）
- 简短的描述文本

### Medium (200px) 适用场景：
- 单位名称（上级单位）
- 地区信息
- 中等长度的描述文本
- 分类选择字段

### Long (300px) 适用场景：
- 较长的名称（职位名称、工作单位）
- 多选数组字段（需要显示多个标签）
- 详细的描述信息（专业要求、其他要求、备注）
- 包含地址的联系信息

## 实际效果

设置完成后，表格将根据每列的内容特点自动调整宽度：
- 数字和简短信息列更紧凑，节省空间
- 重要的长文本列有足够空间完整显示
- 多选标签列有足够空间显示多个选项
- 整体布局更加合理和美观

## 使用方法

在 `data/data.js` 文件中，每个 header 对象现在都包含 `width_type` 字段：

```javascript
{
  excel_column: "A",
  sql_column: "name", 
  name: "职位名称",
  type: "string",
  width_type: "long", // 新增的宽度类型字段
  // ... 其他字段
}
```

系统会自动读取这个字段并设置相应的列宽度。
