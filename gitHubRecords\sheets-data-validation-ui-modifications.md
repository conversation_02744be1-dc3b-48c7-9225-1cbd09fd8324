# sheets-data-validation-ui 修改记录

## 概述
本文档整理了 sheets-data-validation-ui 包的所有 Git 提交修改记录，使用 git diff 格式展示具体的代码变更。

---

## 修改记录

### 1. 修改下拉 (26e2760)
**提交时间**: 2025-07-30 11:24:51
**作者**: yangan

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-multiple-widget.ts`
```diff
@@ -120,33 +120,19 @@ export class DropdownMultipleWidget implements IBaseDataValidationWidget {
         ctx.rect(0, 0, cellWidth - ICON_PLACE, cellHeight);
         ctx.clip();
         ctx.translateWithPrecision(CELL_PADDING_H, CELL_PADDING_V);
-        let top = 0;
-        switch (vt) {
-            case VerticalAlign.MIDDLE:
-                top = (layout.contentHeight - layout.totalHeight) / 2;
-                break;
-            case VerticalAlign.BOTTOM:
-                top = (layout.contentHeight - layout.totalHeight);
-                break;
-            default:
-                break;
-        }
+
+        // 修改：选项整体垂直居中
+        const top = (layout.contentHeight - layout.totalHeight) / 2;
         ctx.translateWithPrecision(0, top);
+
         layout.lines.forEach((line, index) => {
             ctx.save();
-            const { width, height, items } = line;
-            let left = 0;
-            switch (ht) {
-                case HorizontalAlign.RIGHT:
-                    left = layout.contentWidth - width;
-                    break;
-                case HorizontalAlign.CENTER:
-                    left = (layout.contentWidth - width) / 2;
-                    break;
-                default:
-                    break;
-            }
+            const { height, items } = line;
+
+            // 修改：选项整体靠左对齐
+            const left = 0;
             ctx.translate(left, index * (height + MARGIN_V));
+
             items.forEach((item) => {
                 ctx.save();
                 ctx.translateWithPrecision(item.left, 0);
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-widget.ts`
```diff
@@ -28,17 +28,17 @@ import { DROP_DOWN_DEFAULT_COLOR } from '../../const';
 /**
  * padding in Capsule
  */
-const PADDING_H = 4;
+const PADDING_H = 6;
 const ICON_SIZE = 4;
 const ICON_PLACE = 14;
-const PADDING_V = 1;
+const PADDING_V = 2;

 /**
  * margin for Capsule, that means distance between capsule and cell border
  */
 const MARGIN_H = 6;
 const MARGIN_V = 3;
-const RADIUS_BG = 8;
+const RADIUS_BG = 4;
 const DROP_DOWN_ICON_COLOR = '#565656';
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/dropdown.ts`
```diff
@@ -18,9 +18,8 @@ import type { IShapeProps, UniverRenderingContext } from '@univerjs/engine-rende
 import type { IDropdownLayoutInfo } from './layout';
 import { Rect, Shape } from '@univerjs/engine-render';
 import { DROP_DOWN_DEFAULT_COLOR } from '../../../const';
-import { PADDING_H } from './layout';

-const RADIUS = 8;
+const RADIUS = 4;

 export interface IDropdownProps extends IShapeProps {
     fontString: string;
@@ -41,10 +40,18 @@ export class Dropdown extends Shape<IDropdownProps> {
             radius: RADIUS,
             fill: fill || DROP_DOWN_DEFAULT_COLOR,
         });
-        ctx.translateWithPrecision(PADDING_H, layout.ba);
+
+        // 修改：文字在背景框内水平和垂直居中
         ctx.font = fontString;
         ctx.fillStyle = color;
-        ctx.fillText(text, 0, 0);
+        ctx.textAlign = 'center';
+        ctx.textBaseline = 'middle';
+
+        // 在背景框的中心位置绘制文字
+        const centerX = layout.width / 2;
+        const centerY = layout.height / 2;
+        ctx.fillText(text, centerX, centerY);
+
         ctx.restore();
     }
 }
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/layout.ts`
```diff
@@ -18,8 +18,8 @@ import type { IStyleData, Nullable } from '@univerjs/core';
 import type { IDocumentSkeletonFontStyle } from '@univerjs/engine-render';
 import { FontCache, getFontStyleString } from '@univerjs/engine-render';

-export const PADDING_H = 4;
-export const PADDING_V = 0;
+export const PADDING_H = 6;
+export const PADDING_V = 2;
 export const MARGIN_H = 4;
 export const MARGIN_V = 4;
 export const CELL_PADDING_H = 6;
@@ -69,42 +69,41 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
         text: item,
     }));

-    let currentLine: IDropdownLine | undefined;
-    const lines: IDropdownLine[] = [];
+    // 修改：所有选项都放在一行，不换行
+    const singleLine: IDropdownLine = {
+        width: 0,
+        height: 0,
+        items: [],
+    };

-    textLayout.forEach((item) => {
+    let currentLeft = 0;
+    textLayout.forEach((item, index) => {
         const { layout } = item;
         const { width, height } = layout;

-        if (!currentLine || ((currentLine.width + width + MARGIN_H) > widthAvailableForContent)) {
-            currentLine = {
-                width,
-                height,
-                items: [{
-                    ...item,
-                    left: 0,
-                }],
-            };
-            lines.push(currentLine);
-        } else {
-            currentLine.items.push({
+        // 只有在宽度允许的情况下才添加选项，否则截断
+        if (currentLeft + width <= widthAvailableForContent) {
+            singleLine.items.push({
                 ...item,
-                left: currentLine.width + MARGIN_H,
+                left: currentLeft,
             });
-            currentLine.width = currentLine.width + width + MARGIN_H;
-        }
-    });
-    let totalHeight = 0;
-    let maxLineWidth = 0;
-    lines.forEach((line, index) => {
-        maxLineWidth = Math.max(maxLineWidth, line.width);
-        if (index === lines.length - 1) {
-            totalHeight += line.height;
-        } else {
-            totalHeight += line.height + MARGIN_V;
+            singleLine.width = currentLeft + width;
+            singleLine.height = Math.max(singleLine.height, height);
+
+            // 如果不是最后一个选项，添加间距
+            if (index < textLayout.length - 1) {
+                currentLeft += width + MARGIN_H;
+            } else {
+                currentLeft += width;
+            }
         }
+        // 超出宽度的选项直接忽略（截断）
     });

+    const lines = [singleLine];
+    const totalHeight = singleLine.height;
+    const maxLineWidth = singleLine.width;
+
     return {
         lines,
         totalHeight,
```

---

### 2. 修改下拉样式 (02f87c4)
**提交时间**: 2025-07-30 11:29:18
**作者**: yangan

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/dropdown-multiple-widget.ts`
```diff
@@ -136,6 +136,14 @@ export class DropdownMultipleWidget implements IBaseDataValidationWidget {
             items.forEach((item) => {
                 ctx.save();
                 ctx.translateWithPrecision(item.left, 0);
+
+                // 如果选项被截断，设置裁剪区域
+                if (item.clipped && item.clippedWidth) {
+                    ctx.beginPath();
+                    ctx.rect(0, 0, item.clippedWidth, item.layout.height);
+                    ctx.clip();
+                }
+
                 Dropdown.drawWith(ctx as UniverRenderingContext, {
                     ...fontStyle,
                     info: item,
```

#### 文件: `packages/sheets-data-validation-ui/src/views/widgets/shape/layout.ts`
```diff
@@ -57,7 +57,11 @@ export interface IDropdownLayoutInfo {
 export interface IDropdownLine {
     width: number;
     height: number;
-    items: (IDropdownLayoutInfo & { left: number })[];
+    items: (IDropdownLayoutInfo & {
+        left: number;
+        clipped?: boolean;
+        clippedWidth?: number;
+    })[];
 }

 export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFontStyle, cellWidth: number, cellHeight: number) {
@@ -81,8 +85,9 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
         const { layout } = item;
         const { width, height } = layout;

-        // 只有在宽度允许的情况下才添加选项，否则截断
+        // 检查是否有足够空间显示完整选项
         if (currentLeft + width <= widthAvailableForContent) {
+            // 完整显示选项
             singleLine.items.push({
                 ...item,
                 left: currentLeft,
@@ -96,8 +101,24 @@ export function layoutDropdowns(items: string[], fontStyle: IDocumentSkeletonFon
             } else {
                 currentLeft += width;
             }
+        } else if (currentLeft < widthAvailableForContent) {
+            // 部分显示选项（能显示多少就显示多少）
+            const remainingWidth = widthAvailableForContent - currentLeft;
+            if (remainingWidth > 0) {
+                singleLine.items.push({
+                    ...item,
+                    left: currentLeft,
+                    // 添加一个标记表示这个选项被截断了
+                    clipped: true,
+                    clippedWidth: remainingWidth,
+                });
+                singleLine.width = widthAvailableForContent;
+                singleLine.height = Math.max(singleLine.height, height);
+            }
+            // 后续选项都无法显示，直接跳出循环
+            return;
         }
-        // 超出宽度的选项直接忽略（截断）
+        // 完全超出宽度的选项直接忽略
     });

     const lines = [singleLine];
```

---

## 修改总结

### 主要改进点:
1. **布局优化**: 下拉选项垂直居中、左对齐、单行显示
2. **样式调整**: 内边距从4→6、圆角从8→4、文字居中对齐
3. **截断处理**: 超长选项的智能裁剪和部分显示
4. **用户体验**: 改善下拉选项的显示效果和交互体验

### 涉及的核心组件:
- `DropdownMultipleWidget`: 多选下拉组件
- `Dropdown`: 下拉选项形状组件
- `layoutDropdowns`: 下拉选项布局算法

### 技术要点:
- 单行布局算法替换多行布局
- Canvas 裁剪区域的精确控制
- 文字居中对齐的实现
- 选项截断的智能处理
